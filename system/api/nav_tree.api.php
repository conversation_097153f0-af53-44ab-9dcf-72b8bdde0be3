<?php
namespace api\nav_tree;

use system\users;
use edge\Edge;
use const icons\ICONS;
use system\database;
use system\data_importer;

/**
 * Display the form for adding a new navigation entry
 */
function add_nav_entry($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    // Get the parent path from the request
    $parent_path = (!isset($p['parent_path']) || $p['parent_path'] == '') ? 'root' : $p['parent_path'];

    // Render the modal form for adding a new navigation entry
    return Edge::render('nav-entry-form', [
        'parent_path' => $parent_path,
        'icons' => ICONS
    ]);
}

/**
 * Save a new navigation entry to the database
 */
function save_nav_entry($p) {
    print_rr($p,'begin save_nav_entry input_params');
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    // Get form data
    $parent_path = (isset($p['parent_path']) && $p['parent_path'] != '') ? $p['parent_path'] : 'root';
    $key = $p['key'] ?? '';
    $name = $p['name'] ?? '';
    $icon = $p['icon'] ?? 'document';
    $required_roles = $p['required_roles'] ?? [];
    $template = $p['template'] ?? 'none';
    $template_data = $p['template_data'] ?? [];

    // Validate required fields
    if (empty($key) || empty($name)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Key and name are required"}}');
        return;
    }

    // Create view directory structure based on parent path
    $sub_folder = ( $parent_path == 'root' ) ? '' : '/' . $parent_path;
    $view_dir = '/'. normalize_path(FS_VIEWS . $sub_folder);


    // Create directory if it doesn't exist
    if (!file_exists($view_dir)) {
        mkdir($view_dir, 0755, true);
    }

    // Create view file path
    $view_file = $view_dir . '/'. $key . '.edge.php';
    
    // Generate view content based on template
    $view_content = generate_view_content($template, $template_data, $_FILES, $key);
    print_rr($view_file,'view_file');
    // Write view file
    if (file_put_contents($view_file, $view_content)) {
        // View file created successfully
    } else {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to create view file"}}');
        return;
    }

    // Convert required_roles array to JSON for storage
    $required_roles_json = json_encode($required_roles);

    // Insert the new entry with error handling
    print_rr("inserting $name to $parent_path",'try navigation insert',false);
    try {
        $result = database::table('autobooks_navigation')->insert([
            'parent_path' => $parent_path,
            'route_key' => $key,
            'name' => $name,
            'icon' => $icon,
            'required_roles' => $required_roles_json
        ]);

        if ($result) {
            // Log successful navigation entry creation
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'navigation_entry_created',
                    'template' => $template,
                    'template_data' => $template_data,
                    'parent_path' => $parent_path,
                    'route_key' => $key,
                    'name' => $name,
                    'icon' => $icon,
                    'required_roles' => $required_roles,
                    'user_id' => $_SESSION['user_id'] ?? 'Unknown'
                ], 'navigation');
            }

            header('HX-Trigger: {"showNotification": {"type": "success", "message": "Navigation entry added successfully"}}');
        } else {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to add navigation entry"}}');
        }

        // Redirect to refresh the navigation
        header('HX-Redirect: ' . APP_ROOT);

    } catch (\system\DatabaseException $e) {
        // Handle database-specific errors with detailed logging
        $errorData = [
            'action' => 'navigation_entry_creation_failed',
            'error_type' => 'DatabaseException',
            'error_message' => $e->getMessage(),
            'query' => $e->getQuery(),
            'parameters' => $e->getParams(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key,
                'name' => $name,
                'icon' => $icon,
                'required_roles' => $required_roles
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];


            tcs_log($errorData, 'navigation_errors');


        // Return appropriate error response
        if (defined('API_RUN') && API_RUN) {
            // For API calls, return JSON error
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'Database error occurred while saving navigation entry',
                'details' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getDetailedMessage() : 'Please contact support',
                'error_id' => uniqid()
            ]);
        } else {
            // For web requests, show user-friendly error
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Database error: ' .
                   (defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : 'Please try again or contact support') . '"}}');
        }
        return;

    } catch (\Exception $e) {
        // Handle any other unexpected errors
        $errorData = [
            'action' => 'navigation_entry_creation_failed',
            'error_type' => 'GeneralException',
            'error_message' => $e->getMessage(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key,
                'name' => $name,
                'icon' => $icon,
                'required_roles' => $required_roles
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'stack_trace' => $e->getTraceAsString()
        ];
        tcs_log($errorData, 'navigation_errors');
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "An unexpected error occurred. Please try again."}}');

        return;
    }
}

/**
 * Generate view content based on template type and data
 * 
 * @param string $template Template type
 * @param array $template_data Template data from form
 * @param array $files Uploaded files
 * @param string $key Route key
 * @return string Generated view content
 */
function generate_view_content($template, $template_data, $files, $key) {
    // Template directory
    $template_dir = FS_SYS_TEMPLATES;
    print_rr($template,'template');
    switch ($template) {
        case 'data_table':
            // Check if data table template exists
            $template_file = $template_dir . 'data_table_template.php';
            if (!file_exists($template_file)) {
                // Create a default data table template if it doesn't exist
                create_default_data_table_template($template_file);
            }
            tcs_log(['template_file' => $template_file, 'template_data' => $template_data, 'files' => $files, 'key' => $key], 'navigation');
            return process_data_table_template($template_file, $template_data, $files, $key);
            
        case 'custom_html':
            // Check if custom HTML template exists
            $template_file = $template_dir . 'custom_html_template.php';
            if (!file_exists($template_file)) {
                // Create a default custom HTML template if it doesn't exist
                create_default_custom_html_template($template_file);
            }
            
            return process_custom_html_template($template_file, $template_data);
            
        case 'file_upload':
            // For file upload, we'll use the uploaded file as the template
            return process_file_upload_template($files, $key);
            
        case 'none':
        default:
            // Check if default template exists
            $template_file = $template_dir . 'default_template.php';
            if (!file_exists($template_file)) {
                // Create a default template if it doesn't exist
                create_default_template($template_file);
            }
            
            return process_default_template($template_file, $key);
    }
}

/**
 * Create a default data table template
 * 
 * @param string $template_file Path to create the template
 */
function create_default_data_table_template($template_file) {
    // Create directory if it doesn't exist
    $dir = dirname($template_file);
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $template_content = <<<'EOT'
<?php
// Data table template
// This template will be used to generate data table views

// Headers and rows will be replaced with actual data
$headers = /* HEADERS */;
$rows = /* ROWS */;

// Render the data table
echo Edge::render('data-table', [
    'headers' => $headers,
    'rows' => $rows,
    'striped' => true,
    'hover' => true
]);
EOT;
    
    file_put_contents($template_file, $template_content);
}

/**
 * Process data table template with CSV data using data_importer class
 *
 * Uses the data_importer class for robust CSV parsing and error handling.
 * Supports both textarea input and file uploads.
 *
 * @param string $template_file Template file path
 * @param array $template_data Template data from form
 * @param array $files Uploaded files
 * @param string $key Route key
 * @return string Generated view content
 */
function process_data_table_template($template_file, $template_data, $files, $key) {
    $csv_data = '';
    $csv_file_path = '';

    $csv_file_path = FS_UPLOADS .'/' . $key . '_data.csv';

    // Process CSV data from textarea
    if (!empty($template_data['csv'])) {
        $csv_data = $template_data['csv'];
        
        // Save CSV to file for future reference
        
        file_put_contents($csv_file_path, $csv_data);
    }
    // Process uploaded CSV file
    elseif (isset($files['template_file']) && $files['template_file']['error'] === UPLOAD_ERR_OK) {
        // Create directory if it doesn't exist
        if (!file_exists(FS_UPLOADS)) {
            mkdir(FS_UPLOADS, 0755, true);
        }
        
        $file_name = $key . '_' . basename($files['template_file']['name']);
        $csv_file_path = FS_UPLOADS . $file_name;
        
        if (move_uploaded_file($files['template_file']['tmp_name'], $csv_file_path)) {
            $csv_data = file_get_contents($csv_file_path);
        }
    }
    
    // Parse CSV data using data_importer class
    $headers = [];
    $rows = [];

    if (!empty($csv_data)) {
        // Use data_importer for robust CSV parsing
        if (!empty($csv_file_path) && file_exists($csv_file_path)) {
            // Use file-based parsing for uploaded files
            $csv_result = data_importer::read_csv_file($csv_file_path);
        } else {
            // For textarea data, create a temporary file
            $temp_file = tempnam(sys_get_temp_dir(), 'nav_tree_csv_');
            file_put_contents($temp_file, $csv_data);
            $csv_result = data_importer::read_csv_file($temp_file);
            unlink($temp_file); // Clean up temporary file
        }

        // Handle parsing results
        if (isset($csv_result['error'])) {
            // Log the error for debugging
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'csv_parsing_failed',
                    'error' => $csv_result['error'],
                    'key' => $key,
                    'csv_file_path' => $csv_file_path ?? 'textarea_data'
                ], 'nav_tree_template');
            }
            // Fall back to empty data rather than breaking
            $headers = [];
            $rows = [];
        } else {
            $headers = $csv_result['header'] ?? [];
            $rows = $csv_result['rows'] ?? [];

            // Log successful parsing
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'csv_parsing_successful',
                    'key' => $key,
                    'headers_count' => count($headers),
                    'rows_count' => count($rows),
                    'csv_file_path' => $csv_file_path ?? 'textarea_data'
                ], 'nav_tree_template');
            }
        }
    }
    
    // Get template content
    $template_content = file_get_contents($template_file);
    
    // Replace placeholders with actual data
    $template_content = str_replace('/* HEADERS */', var_export($headers, true), $template_content);
    $template_content = str_replace('/* ROWS */', var_export($rows, true), $template_content);
    
    return $template_content;
}

/**
 * Create a default custom HTML template
 * 
 * @param string $template_file Path to create the template
 */
function create_default_custom_html_template($template_file) {
    // Create directory if it doesn't exist
    $dir = dirname($template_file);
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $template_content = <<<'EOT'
<?php
// Custom HTML template
// This template will be used to generate custom HTML views

// The HTML content will be replaced with actual content
?>
/* HTML_CONTENT */
EOT;
    
    file_put_contents($template_file, $template_content);
}

/**
 * Process custom HTML template
 * 
 * @param string $template_file Template file path
 * @param array $template_data Template data from form
 * @return string Generated view content
 */
function process_custom_html_template($template_file, $template_data) {
    $html = $template_data['html'] ?? '';
    
    // Get template content
    $template_content = file_get_contents($template_file);
    
    // Replace placeholder with actual HTML
    $template_content = str_replace('/* HTML_CONTENT */', $html, $template_content);
    
    return $template_content;
}

/**
 * Process file upload as template
 * 
 * @param array $files Uploaded files
 * @param string $key Route key
 * @return string Generated view content
 */
function process_file_upload_template($files, $key) {
    if (isset($files['template_file']) && $files['template_file']['error'] === UPLOAD_ERR_OK) {
        
        // Create directory if it doesn't exist
        if (!file_exists(FS_UPLOADS)) {
            mkdir(FS_UPLOADS, 0755, true);
        }
        
        $file_name = $key . '_' . basename($files['template_file']['name']);
        $upload_path = FS_UPLOADS . $file_name;
        
        if (move_uploaded_file($files['template_file']['tmp_name'], $upload_path)) {
            $file_content = file_get_contents($upload_path);
            $extension = pathinfo($upload_path, PATHINFO_EXTENSION);
            
            if ($extension === 'php') {
                // For PHP files, use the content directly
                return $file_content;
            } elseif ($extension === 'html') {
                // For HTML files, wrap in PHP tags
                return "<?php\n// Auto-generated view from uploaded HTML file\n?>\n" . $file_content;
            } else {
                // For other files, create a PHP file that outputs the content
                return "<?php\n// Auto-generated view from uploaded file\necho file_get_contents('" . $upload_path . "');\n";
            }
        }
    }
    
    // Default content if file upload failed
    return "<?php\n// Auto-generated view (file upload failed)\necho '<div class=\"p-4 text-red-500\">No template file was uploaded or upload failed</div>';\n";
}

/**
 * Create a default template
 * 
 * @param string $template_file Path to create the template
 */
function create_default_template($template_file) {
    // Create directory if it doesn't exist
    $dir = dirname($template_file);
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $template_content = <<<'EOT'
        <?php
            // Default template
            // This template will be used to generate default views
            
            echo '<div class="p-4">';
            echo '<h1 class="text-2xl font-bold mb-4">Welcome to /* PAGE_KEY */</h1>';
            echo '<p>This is a default view. Edit this file to customize the content.</p>';
            echo '</div>';
    EOT;
    
    file_put_contents($template_file, $template_content);
}

/**
 * Process default template
 * 
 * @param string $template_file Template file path
 * @param string $key Route key
 * @return string Generated view content
 */
function process_default_template($template_file, $key) {
    // Get template content
    $template_content = file_get_contents($template_file);
    
    // Replace placeholder with actual key
    $template_content = str_replace('/* PAGE_KEY */', $key, $template_content);
    
    return $template_content;
}

/**
 * Delete a navigation entry from the database and filesystem
 */
function delete_nav_entry($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);
    print_rr($p,'POST_params');
    print_rr($_GET,'GET_params');
    print_rr($p,'begin delete_nav_entry input_params');
    // Get form data
    $parent_path = (!isset($p['parent_path']) || $p['parent_path'] == '') ? 'root' : $p['parent_path'];
    $key = $p['key'] ?? '';

    print_rr($parent_path,'parent_path');
    print_rr($key,'key');
    // Validate required fields
    if (empty($key)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Route key is required"}}');
        return;
    }
    print_rr($parent_path,'parent_path');
    try {
        // First, get the navigation entry to confirm it exists
        $entry = database::table('autobooks_navigation')
            ->select(['id', 'name', 'route_key'])
            ->where('parent_path', $parent_path)
            ->where('route_key', $key)
            ->first();

        print_rr($entry,'entry');
        if (!$entry) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Navigation entry not found"}}');
            return;
        }
        print_rr($entry,'entry');
        // Delete from database
        $result = database::table('autobooks_navigation')
            ->where('parent_path', $parent_path)
            ->where('route_key', $key)
            ->delete();
        print_rr($result,'result');
        if ($result) {
            // Delete the corresponding view file
            $view_dir = FS_VIEWS;
            if (!empty($parent_path)) {
                $path_parts = explode('/', trim($parent_path, '/'));
                $view_dir .= implode('/', $path_parts) . '/';
            }
            $view_file = $view_dir . $key . '.edge.php';

            if (file_exists($view_file)) {
                unlink($view_file);
            }

            // Log successful navigation entry deletion
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'navigation_entry_deleted',
                    'parent_path' => $parent_path,
                    'route_key' => $key,
                    'name' => $entry['name'],
                    'user_id' => $_SESSION['user_id'] ?? 'Unknown'
                ], 'navigation');
            }

            header('HX-Trigger: {"showNotification": {"type": "success", "message": "Navigation entry deleted successfully"}}');
        } else {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to delete navigation entry"}}');
        }
        print_rr($result,'result');
        // Redirect to refresh the navigation
        //header('HX-Redirect: ' . APP_ROOT);

    } catch (\system\DatabaseException $e) {
        // Handle database-specific errors with detailed logging
        $errorData = [
            'action' => 'navigation_entry_deletion_failed',
            'error_type' => 'DatabaseException',
            'error_message' => $e->getMessage(),
            'query' => $e->getQuery(),
            'parameters' => $e->getParams(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];

        tcs_log($errorData, 'navigation_errors');

        // Return appropriate error response
        if (defined('API_RUN') && API_RUN) {
            // For API calls, return JSON error
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'Database error occurred while deleting navigation entry',
                'details' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getDetailedMessage() : 'Please contact support',
                'error_id' => uniqid()
            ]);
        } else {
            // For web requests, show user-friendly error
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Database error: ' .
                   (defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : 'Please try again or contact support') . '"}}');
        }
        return;

    } catch (\Exception $e) {
        // Handle any other unexpected errors
        $errorData = [
            'action' => 'navigation_entry_deletion_failed',
            'error_type' => 'GeneralException',
            'error_message' => $e->getMessage(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'stack_trace' => $e->getTraceAsString()
        ];
        tcs_log($errorData, 'navigation_errors');
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "An unexpected error occurred. Please try again."}}');

        return;
    }
}

/**
 * Get navigation items from the database
 *
 * @param string $parent_path The parent path to get items for
 * @return array Navigation items
 */
function get_navigation_items($parent_path = '') {
    try {
        // Check if the navigation table exists
        if (!database::schema()::hasTable('autobooks_navigation')) {
            // Table doesn't exist yet, return empty array
            return [];
        }

        // Get navigation items for the given parent path
        $results = database::table('autobooks_navigation')
            ->select(['route_key', 'name', 'icon', 'required_roles'])
            ->where('parent_path', $parent_path)
            ->orderBy('name', 'asc')
            ->get();

        $items = [];
        foreach ($results as $row) {
            $key = $row['route_key'];
            $items[$key] = [
                'name' => $row['name'],
                'icon' => $row['icon'],
                'required_roles' => json_decode($row['required_roles'], true)
            ];

            // Check for sub-items
            $sub_path = $parent_path . $key . '/';
            $sub_items = get_navigation_items($sub_path);
            if (!empty($sub_items)) {
                $items[$key]['sub_folder'] = $sub_items;
            }
        }

        return $items;

    } catch (\system\DatabaseException $e) {
        // Log database error but don't break the navigation
        if (function_exists('tcs_log')) {
            tcs_log([
                'action' => 'get_navigation_items_failed',
                'error_type' => 'DatabaseException',
                'error_message' => $e->getMessage(),
                'query' => $e->getQuery(),
                'parameters' => $e->getParams(),
                'parent_path' => $parent_path,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown'
            ], 'navigation_errors');
        }

        // Return empty array to prevent breaking the navigation
        return [];

    } catch (\Exception $e) {
        // Log any other errors
        if (function_exists('tcs_log')) {
            tcs_log([
                'action' => 'get_navigation_items_failed',
                'error_type' => 'GeneralException',
                'error_message' => $e->getMessage(),
                'parent_path' => $parent_path,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown',
                'stack_trace' => $e->getTraceAsString()
            ], 'navigation_errors');
        }

        // Return empty array to prevent breaking the navigation
        return [];
    }
}


