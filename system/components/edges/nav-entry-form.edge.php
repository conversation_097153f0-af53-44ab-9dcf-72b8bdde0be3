@props([
    'parent_path' => '',
    'icons' => [],
    'templates' => [
        'none' => 'None',
        'data_table' => 'Data Table',
        'custom_html' => 'Custom HTML',
        'file_upload' => 'File Upload'
    ]
])

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>
    <form
        hx-post="{{ APP_ROOT }}api/system/nav_tree/save_nav_entry"
        hx-target="#content_wrapper"
        hx-swap="innerHTML"
        @submit="$dispatch('hide-modal')"
        class="space-y-4"
        x-data="{ selectedTemplate: 'none' }"
        enctype="multipart/form-data"
    >
        <input type="hidden" name="parent_path" value="{{ $parent_path }}">
        
        <div>
            <x-forms-input 
                name="key" 
                label="Route Key" 
                placeholder="e.g., dashboard" 
                required
            />
        </div>
        
        <div>
            <x-forms-input 
                name="name" 
                label="Display Name" 
                placeholder="e.g., Dashboard" 
                required
            />
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Icon</label>
            <x-forms-select-with-icons name="icon" :options="array_keys(ICONS)"></x-forms-select-with-icons>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Template</label>
            <select 
                name="template" 
                x-model="selectedTemplate"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $label)
                    <option value="{{ $key }}">{{ $label }}</option>
                @endforeach
            </select>
        </div>
        
        <!-- Template-specific fields -->
        <div x-show="selectedTemplate === 'data_table'" class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">CSV Data</label>
                <textarea 
                    name="template_data[csv]" 
                    rows="4" 
                    placeholder="Paste CSV data here..."
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                ></textarea>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Or Upload CSV File</label>
                <input 
                    type="file" 
                    name="csv_file"
                    accept=".csv"
                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                />
            </div>
        </div>
        
        <div x-show="selectedTemplate === 'custom_html'" class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Custom HTML</label>
                <textarea 
                    name="template_data[html]" 
                    rows="6" 
                    placeholder="Enter custom HTML here..."
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                ></textarea>
            </div>
        </div>
        
        <div x-show="selectedTemplate === 'file_upload'" class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Upload Template File</label>
                <input 
                    type="file" 
                    name="template_file" 
                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                />
            </div>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Required Roles</label>
            <div class="space-y-2">
                @foreach(['user', 'admin', 'dev'] as $role)
                    <div class="flex items-center">
                        <input type="checkbox" name="required_roles[]" value="{{ $role }}" id="role_{{ $role }}" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        <label for="role_{{ $role }}" class="ml-2 block text-sm text-gray-900">{{ ucfirst($role) }}</label>
                    </div>
                @endforeach
            </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
            <button 
                type="button" 
                @click="$dispatch('hide-modal')"
                class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
                Cancel
            </button>
            <button 
                type="submit"
                class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
                Save
            </button>
        </div>
    </form>
</div>