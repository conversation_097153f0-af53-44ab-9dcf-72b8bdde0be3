@props([
    'items' => [],
    'parent_path' => 'root',
    'depth' => 0,
 ]
)
@if($parent_path == 'root')
    @php
        $parent_path = '';
    @endphp
@endif

<ul
    @switch ($depth)
        @case(0) x-data="initNavTree()" x-init="init()"
        @default class="bg-indigo-{{ $depth+6 }}00 shadow-inner shadow-indigo-{{ $depth+7 }}00"
    @endswitch
>
    @print_rr($items,'nav-tree items')
    @foreach($items as $key => $item)
        @if( count($item['required_roles']) > 0 && !in_array(USER_ROLE, $item['required_roles']) ) @php continue; @endphp @endif
        <li :class='collapsed ? "mx-0" : "mx-0"' class="relative group">
            @php
                $item_route = trim($parent_path . $key, '/');
            @endphp
            <a href='{{ $parent_path }}{{ $key }}'
               hx-get='{{ APP_ROOT }}{{ $parent_path }}{{ $key }}'
               hx-replace-url='{{ APP_ROOT }}{{ $parent_path }}{{ $key }}'
               hx-target='#content_wrapper' {{ $folder_out }}
               class='flex gap-x-3 p-2 text-sm font-semibold leading-6 group hover:text-white inset-shadow-sm'
               :class="currentRoute === '{{ $item_route }}' || (currentRoute.startsWith('{{ $item_route }}/') && {{ isset($item['sub_folder']) ? 'true' : 'false' }}) ? 'border-y-2 border-y-green-600 text-white bg-green-700' : 'text-indigo-200  hover:bg-indigo-{{ $depth + 6 + 1 }}00'"
               x-state:on='Current' x-state:off='Default'
               x-state="currentRoute === '{{ $item_route }}' || (currentRoute.startsWith('{{ $item_route }}/') && {{ isset($item['sub_folder']) ? 'true' : 'false' }}) ? 'on' : 'off'"
               @click="currentRoute = '{{ $item_route }}'; localStorage.setItem('currentNavRoute', '{{ $item_route }}'); window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: '{{ $item_route }}' } }));"
               x-state-description='Current: "border-y-2 border-y-green-600 text-white bg-green-700", Default: "border-y-2 border-y-indigo-{{ $depth+6 }}00 text-indigo-200 hover:text-white hover:bg-indigo-700"'>
                <span class='shrink-0'>{{ ICONS[$item['icon']] }}</span>
                <span class='transition-opacity duration-300 whitespace-nowrap' :class='collapsed ? "opacity-0" : "opacity-100"'>
                    {{ $item['name'] }}
                </span>
            </a>
            @if(in_array(USER_ROLE, ['admin', 'dev']))
                <button
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-red-400 hover:text-red-600 p-1 rounded"
                    hx-delete="{{ APP_ROOT }}api/system/nav_tree/delete_nav_entry"
                    hx-vals='{"parent_path": "{{ $parent_path }}", "key": "{{ $key }}"}'
                    hx-confirm="Are you sure you want to delete '{{ $item['name'] }}'? This action cannot be undone."
                    hx-target="closest li"
                    title="Delete {{ $item['name'] }}"
                    @click.stop
                >
                    <span class="shrink-0 w-4 h-4 block">
                        @icon('trash')
                    </span>
                </button>
            @endif
            @if(isset($item['sub_folder']))
                 <x-nav-tree :items="$item['sub_folder']" :parent_path="$parent_path . $key . '/'" :depth="$depth + 1">
            @endif
        </li>
    @endforeach

    @if(in_array(USER_ROLE, ['admin', 'dev']))
        <li :class='collapsed ? "mx-1" : "mx-2"' class="mt-2">
            <button
                hx-post="{{ APP_ROOT }}api/system/nav_tree/add_nav_entry"
                hx-vals='{"parent_path": "{{ $parent_path }}"}'
                hx-target="#modal_body"
                @click='showModal = true'
                class="flex gap-x-3 p-2 text-sm font-semibold leading-6 text-green-400 rounded-md group hover:text-white hover:bg-green-700 w-full"
            >
                <span class="shrink-0">{{ ICONS['plus'] }}</span>
                <span class="transition-opacity duration-300 whitespace-nowrap" :class='collapsed ? "opacity-0" : "opacity-100"'>
                    Add Entry
                </span>
            </button>
        </li>
    @endif
</ul>